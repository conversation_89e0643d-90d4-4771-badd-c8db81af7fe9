import type { GeneratedNote } from "@/lib/types/image-batch-generator"

// 使用内存存储来避免 sessionStorage 容量限制
class GeneratedNotesStore {
  private notes: GeneratedNote[] = []
  private listeners: Array<() => void> = []

  setNotes(notes: GeneratedNote[]) {
    this.notes = notes
    this.notifyListeners()
  }

  getNotes(): GeneratedNote[] {
    return this.notes
  }

  clearNotes() {
    this.notes = []
    this.notifyListeners()
  }

  subscribe(listener: () => void) {
    this.listeners.push(listener)
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener)
    }
  }

  private notifyListeners() {
    this.listeners.forEach(listener => listener())
  }
}

// 单例实例
export const generatedNotesStore = new GeneratedNotesStore()