"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON><PERSON>, Loader2 } from "lucide-react"
import { NotesList } from "@/components/image-batch-generator/notes-list"
import { NoteEditorModern } from "@/components/image-batch-generator/note-editor-modern"
import { NoteEditorSimplified } from "@/components/image-batch-generator/note-editor-simplified"
import { NoteEditorHorizontal } from "@/components/image-batch-generator/note-editor-horizontal"
import { LayoutGrid, LayoutList, Columns } from "lucide-react"
import type { GeneratedNote } from "@/lib/types/image-batch-generator"
import { toast } from "@/components/ui/use-toast"
import { generatedNotesStore } from "@/lib/stores/generated-notes-store"

export default function BatchGenerationResultsPage() {
  const router = useRouter()
  const [notes, setNotes] = useState<GeneratedNote[]>([])
  const [selectedNoteId, setSelectedNoteId] = useState<string | null>(null)
  const [editorLayout, setEditorLayout] = useState<'split' | 'modern' | 'horizontal'>('horizontal')
  const [isSaving, setIsSaving] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  // 从内存存储获取生成的笔记数据
  useEffect(() => {
    const storedNotes = generatedNotesStore.getNotes()
    if (storedNotes && storedNotes.length > 0) {
      setNotes(storedNotes)
      setSelectedNoteId(storedNotes[0].id)
    }
    setIsLoading(false)

    // 订阅更新
    const unsubscribe = generatedNotesStore.subscribe(() => {
      const updatedNotes = generatedNotesStore.getNotes()
      setNotes(updatedNotes)
      if (updatedNotes.length > 0 && !selectedNoteId) {
        setSelectedNoteId(updatedNotes[0].id)
      }
    })

    return () => {
      unsubscribe()
    }
  }, [])

  const selectedNote = notes.find(note => note.id === selectedNoteId)
  const selectedNoteIndex = notes.findIndex(note => note.id === selectedNoteId)

  const handleDownloadAll = () => {
    notes.forEach((note, index) => {
      const link = document.createElement('a')
      link.href = note.coverImage
      link.download = `note_${index + 1}_cover.jpg`
      link.click()
    })
  }

  const handleUpdateNote = (noteId: string, updates: Partial<GeneratedNote>) => {
    setNotes(prev => {
      const updatedNotes = prev.map(note => 
        note.id === noteId ? { ...note, ...updates } : note
      )
      // 更新内存存储
      generatedNotesStore.setNotes(updatedNotes)
      return updatedNotes
    })
  }

  const handleNavigate = (direction: 'prev' | 'next') => {
    const currentIndex = notes.findIndex(note => note.id === selectedNoteId)
    if (currentIndex === -1) return

    const newIndex = direction === 'prev' ? currentIndex - 1 : currentIndex + 1
    if (newIndex >= 0 && newIndex < notes.length) {
      setSelectedNoteId(notes[newIndex].id)
    }
  }

  // 上传blob到R2存储
  const uploadBlobToR2 = async (blobUrl: string, filename: string, retries = 3): Promise<string> => {
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        const response = await fetch(blobUrl)
        const blob = await response.blob()
        
        const formData = new FormData()
        formData.append('file', blob, filename)
        formData.append('folder', 'batch-generator')
        
        const uploadResponse = await fetch('/api/storage/upload', {
          method: 'POST',
          body: formData
        })
        
        if (!uploadResponse.ok) {
          throw new Error(`Upload failed: ${uploadResponse.status}`)
        }
        
        const result = await uploadResponse.json()
        return result.url
      } catch (error) {
        console.error(`Failed to upload ${filename} (attempt ${attempt}/${retries}):`, error)
        
        if (attempt === retries) {
          console.warn(`All upload attempts failed for ${filename}, falling back to base64`)
          return blobToBase64(blobUrl)
        }
        
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt))
      }
    }
    
    return blobToBase64(blobUrl)
  }

  // 将blob URL转换为base64
  const blobToBase64 = async (blobUrl: string): Promise<string> => {
    try {
      const response = await fetch(blobUrl)
      const blob = await response.blob()
      
      if (blob.size > 500 * 1024) {
        console.warn(`Image too large for base64: ${blob.size} bytes`)
        return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
      }
      
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onloadend = () => resolve(reader.result as string)
        reader.onerror = reject
        reader.readAsDataURL(blob)
      })
    } catch (error) {
      console.error('Failed to convert blob to base64:', error)
      return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
    }
  }

  const handleSaveToDatabase = async () => {
    if (notes.length === 0) {
      toast({
        variant: "destructive",
        title: "没有可保存的内容",
        description: "请先生成笔记"
      })
      return
    }

    setIsSaving(true)
    
    const uploadToast = toast({
      title: "正在上传图片...",
      description: "准备上传图片到云存储",
      duration: Infinity
    })
    
    try {
      const sessionName = `批量生成_${new Date().toLocaleDateString('zh-CN')}_${new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}`
      
      // 收集所有需要上传的图片
      const timestamp = Date.now()
      const uploadTasks: Array<{
        noteIndex: number
        type: 'cover' | 'content'
        imageIndex: number
        blobUrl: string
        filename: string
      }> = []

      notes.forEach((note, noteIndex) => {
        if (note.coverImage.startsWith('blob:')) {
          uploadTasks.push({
            noteIndex,
            type: 'cover',
            imageIndex: 0,
            blobUrl: note.coverImage,
            filename: `cover_${timestamp}_${noteIndex}.jpg`
          })
        }

        note.images.forEach((img, imgIndex) => {
          if (img.startsWith('blob:')) {
            uploadTasks.push({
              noteIndex,
              type: 'content',
              imageIndex: imgIndex,
              blobUrl: img,
              filename: `content_${timestamp}_${noteIndex}_${imgIndex}.jpg`
            })
          }
        })
      })

      console.log(`Starting upload of ${uploadTasks.length} images...`)

      // 批量并发上传
      const uploadResults = new Map<string, string>()
      let uploadedCount = 0
      const concurrentLimit = 10
      let currentIndex = 0
      
      const uploadQueue = async () => {
        while (currentIndex < uploadTasks.length) {
          const taskIndex = currentIndex++
          const task = uploadTasks[taskIndex]
          
          try {
            const url = await uploadBlobToR2(task.blobUrl, task.filename)
            uploadResults.set(task.blobUrl, url)
            uploadedCount++
            
            uploadToast.update({
              id: uploadToast.id,
              title: "正在上传图片...",
              description: `已上传 ${uploadedCount}/${uploadTasks.length} 张图片 (${Math.round(uploadedCount / uploadTasks.length * 100)}%)`,
              duration: Infinity
            })
            
            console.log(`Uploaded ${task.filename} (${uploadedCount}/${uploadTasks.length})`)
          } catch (error) {
            console.error(`Failed to upload ${task.filename}:`, error)
            const base64 = await blobToBase64(task.blobUrl)
            uploadResults.set(task.blobUrl, base64)
            uploadedCount++
          }
        }
      }
      
      const uploaders = Array(Math.min(concurrentLimit, uploadTasks.length))
        .fill(null)
        .map(() => uploadQueue())
      
      await Promise.all(uploaders)

      console.log('All images uploaded successfully')
      uploadToast.dismiss()

      // 构建笔记数据
      const notesData = notes.map((note, index) => ({
        title: note.title,
        content: note.content,
        coverImage: uploadResults.get(note.coverImage) || note.coverImage,
        contentImages: note.images.map(img => uploadResults.get(img) || img),
        keywords: note.keywords,
        templateSettings: {},
        textOverlayConfig: note.textOverlay,
        generationConfig: {}
      }))

      const response = await fetch('/api/image-batch-generator', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sessionName,
          notes: notesData
        })
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        console.error('Save failed:', errorData)
        throw new Error(errorData.error || '保存失败')
      }

      const result = await response.json()
      
      toast({
        title: "保存成功",
        description: `成功保存 ${result.notesCreated} 个笔记到数据库`
      })

      // 清除内存存储并返回
      generatedNotesStore.clearNotes()
      router.push('/dashboard/image-batch-generator/history')
      
    } catch (error) {
      toast({
        variant: "destructive",
        title: "保存失败",
        description: error instanceof Error ? error.message : "请稍后重试"
      })
    } finally {
      setIsSaving(false)
    }
  }

  const handleClose = () => {
    generatedNotesStore.clearNotes()
    router.push('/dashboard/image-batch-generator')
  }

  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (notes.length === 0) {
    return (
      <div className="flex-1 flex flex-col items-center justify-center gap-4">
        <p className="text-lg text-muted-foreground">没有找到生成的笔记</p>
        <Button onClick={() => router.push('/dashboard/image-batch-generator')}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          返回生成页面
        </Button>
      </div>
    )
  }

  const navigationProps = {
    onNavigate: handleNavigate,
    canNavigate: {
      prev: selectedNoteIndex > 0,
      next: selectedNoteIndex < notes.length - 1
    }
  }

  return (
    <div className="flex h-screen bg-background">
      {/* 左侧笔记列表 */}
      <NotesList
        notes={notes}
        selectedNoteId={selectedNoteId}
        onSelectNote={setSelectedNoteId}
        onClose={handleClose}
        onDownloadAll={handleDownloadAll}
        onSaveToDatabase={handleSaveToDatabase}
        isSaving={isSaving}
      />
      
      {/* 右侧编辑器 */}
      <div className="flex-1 flex flex-col bg-background relative">
        {/* 顶部工具栏 */}
        <div className="flex items-center justify-between px-6 py-4 border-b">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push('/dashboard/image-batch-generator')}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回
            </Button>
            <h1 className="text-xl font-semibold">批量生成结果</h1>
          </div>
          
          {/* 布局切换按钮 */}
          <div className="flex gap-1 bg-muted rounded-lg p-1">
            <Button
              variant={editorLayout === 'horizontal' ? 'default' : 'ghost'}
              size="icon"
              className="h-8 w-8"
              onClick={() => setEditorLayout('horizontal')}
              title="水平布局"
            >
              <Columns className="h-4 w-4" />
            </Button>
            <Button
              variant={editorLayout === 'modern' ? 'default' : 'ghost'}
              size="icon"
              className="h-8 w-8"
              onClick={() => setEditorLayout('modern')}
              title="现代布局"
            >
              <LayoutList className="h-4 w-4" />
            </Button>
            <Button
              variant={editorLayout === 'split' ? 'default' : 'ghost'}
              size="icon"
              className="h-8 w-8"
              onClick={() => setEditorLayout('split')}
              title="分栏布局"
            >
              <LayoutGrid className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* 编辑器内容 */}
        <div className="flex-1 overflow-hidden">
          {editorLayout === 'horizontal' ? (
            <NoteEditorHorizontal
              note={selectedNote}
              noteIndex={selectedNoteIndex}
              onUpdateNote={(updates) => selectedNoteId && handleUpdateNote(selectedNoteId, updates)}
              {...navigationProps}
            />
          ) : editorLayout === 'modern' ? (
            <NoteEditorModern
              note={selectedNote}
              noteIndex={selectedNoteIndex}
              onUpdateNote={(updates) => selectedNoteId && handleUpdateNote(selectedNoteId, updates)}
              {...navigationProps}
            />
          ) : (
            <NoteEditorSimplified
              note={selectedNote}
              noteIndex={selectedNoteIndex}
              onUpdateNote={(updates) => selectedNoteId && handleUpdateNote(selectedNoteId, updates)}
              {...navigationProps}
            />
          )}
        </div>
      </div>
    </div>
  )
}