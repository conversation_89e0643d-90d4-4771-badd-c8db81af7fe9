"use client"

import { Card } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { X, Download, Save, Loader2 } from "lucide-react"
import { cn } from "@/lib/utils"
import type { GeneratedNote } from "@/lib/types/image-batch-generator"

interface NotesListProps {
  notes: GeneratedNote[]
  selectedNoteId: string | null
  onSelectNote: (noteId: string) => void
  onClose: () => void
  onDownloadAll: () => void
  onSaveToDatabase: () => void
  isSaving: boolean
}

export function NotesList({ 
  notes, 
  selectedNoteId, 
  onSelectNote, 
  onClose,
  onDownloadAll,
  onSaveToDatabase,
  isSaving
}: NotesListProps) {
  return (
    <div className="w-96 border-r bg-muted/30 flex flex-col">
      <div className="p-4 border-b bg-background">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold">生成的笔记 ({notes.length})</h2>
          <Button
            size="sm"
            variant="ghost"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      <div className="flex-1 overflow-y-auto p-4">
        <div className="space-y-3">
          {notes.map((note, index) => (
            <Card
              key={note.id}
              className={cn(
                "cursor-pointer transition-all hover:shadow-md",
                selectedNoteId === note.id && "ring-2 ring-primary"
              )}
              onClick={() => onSelectNote(note.id)}
            >
              <div className="flex gap-3 p-3">
                <div className="relative w-24 h-32 flex-shrink-0 bg-muted/50 rounded overflow-hidden">
                  <img
                    src={note.coverImage}
                    alt={note.title}
                    className="w-full h-full object-contain"
                  />
                  <Badge className="absolute top-1 left-1 text-xs" variant="secondary">
                    #{index + 1}
                  </Badge>
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="font-medium text-sm line-clamp-2 mb-1">
                    {note.title}
                  </h3>
                  <p className="text-xs text-muted-foreground line-clamp-2 mb-2">
                    {note.content.substring(0, 50)}...
                  </p>
                  <div className="flex flex-wrap gap-1">
                    {note.keywords.slice(0, 3).map((keyword, idx) => (
                      <span key={idx} className="text-xs text-primary">
                        #{keyword}
                      </span>
                    ))}
                  </div>
                  <div className="flex items-center gap-2 mt-2 text-xs text-muted-foreground">
                    <span>{note.images.length} 张图片</span>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
      
      <div className="p-4 border-t bg-background space-y-2">
        <Button
          className="w-full"
          onClick={onSaveToDatabase}
          disabled={isSaving}
        >
          {isSaving ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              保存中...
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              保存到数据库
            </>
          )}
        </Button>
        <Button
          className="w-full"
          variant="outline"
          onClick={onDownloadAll}
        >
          <Download className="mr-2 h-4 w-4" />
          批量下载所有封面
        </Button>
      </div>
    </div>
  )
}