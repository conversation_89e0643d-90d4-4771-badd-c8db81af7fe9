"use client"

import { useState } from "react"
import { NotesList } from "./notes-list"
import { NoteEditorSimplified } from "./note-editor-simplified"
import { NoteEditorModern } from "./note-editor-modern"
import { NoteEditorHorizontal } from "./note-editor-horizontal"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { LayoutGrid, LayoutList, Columns } from "lucide-react"
import type { GeneratedNote } from "@/lib/types/image-batch-generator"

interface BatchGenerationResultsV2Props {
  notes: GeneratedNote[]
  selectedNoteId: string | null
  onSelectNote: (noteId: string) => void
  onClose: () => void
  onUpdateNote: (noteId: string, updates: Partial<GeneratedNote>) => void
  onSaveToDatabase: () => void
  isSaving: boolean
}

export function BatchGenerationResultsV2({
  notes,
  selectedNoteId,
  onSelectNote,
  onClose,
  onUpdateNote,
  onSaveToDatabase,
  isSaving
}: BatchGenerationResultsV2Props) {
  const [editorLayout, setEditorLayout] = useState<'split' | 'modern' | 'horizontal'>('horizontal')
  const selectedNote = notes.find(note => note.id === selectedNoteId)
  const selectedNoteIndex = notes.findIndex(note => note.id === selectedNoteId)

  const handleDownloadAll = () => {
    notes.forEach((note, index) => {
      const link = document.createElement('a')
      link.href = note.coverImage
      link.download = `note_${index + 1}_cover.jpg`
      link.click()
    })
  }

  const handleUpdateSelectedNote = (updates: Partial<GeneratedNote>) => {
    if (selectedNoteId) {
      onUpdateNote(selectedNoteId, updates)
    }
  }

  const handleNavigate = (direction: 'prev' | 'next') => {
    const currentIndex = notes.findIndex(note => note.id === selectedNoteId)
    if (currentIndex === -1) return

    const newIndex = direction === 'prev' ? currentIndex - 1 : currentIndex + 1
    if (newIndex >= 0 && newIndex < notes.length) {
      onSelectNote(notes[newIndex].id)
    }
  }

  const navigationProps = {
    onNavigate: handleNavigate,
    canNavigate: {
      prev: selectedNoteIndex > 0,
      next: selectedNoteIndex < notes.length - 1
    }
  }

  return (
    <div className="fixed inset-0 bg-background z-50 flex">
      <NotesList
        notes={notes}
        selectedNoteId={selectedNoteId}
        onSelectNote={onSelectNote}
        onClose={onClose}
        onDownloadAll={handleDownloadAll}
        onSaveToDatabase={onSaveToDatabase}
        isSaving={isSaving}
      />
      
      <div className="flex-1 flex flex-col bg-background relative">
        {/* 布局切换按钮 */}
        <div className="absolute top-4 right-4 z-10">
          <div className="flex gap-1 bg-muted rounded-lg p-1">
            <Button
              variant={editorLayout === 'horizontal' ? 'default' : 'ghost'}
              size="icon"
              className="h-8 w-8"
              onClick={() => setEditorLayout('horizontal')}
              title="水平布局"
            >
              <Columns className="h-4 w-4" />
            </Button>
            <Button
              variant={editorLayout === 'modern' ? 'default' : 'ghost'}
              size="icon"
              className="h-8 w-8"
              onClick={() => setEditorLayout('modern')}
              title="现代布局"
            >
              <LayoutList className="h-4 w-4" />
            </Button>
            <Button
              variant={editorLayout === 'split' ? 'default' : 'ghost'}
              size="icon"
              className="h-8 w-8"
              onClick={() => setEditorLayout('split')}
              title="分栏布局"
            >
              <LayoutGrid className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* 编辑器 */}
        {editorLayout === 'horizontal' ? (
          <NoteEditorHorizontal
            note={selectedNote}
            noteIndex={selectedNoteIndex}
            onUpdateNote={handleUpdateSelectedNote}
            {...navigationProps}
          />
        ) : editorLayout === 'modern' ? (
          <NoteEditorModern
            note={selectedNote}
            noteIndex={selectedNoteIndex}
            onUpdateNote={handleUpdateSelectedNote}
            {...navigationProps}
          />
        ) : (
          <NoteEditorSimplified
            note={selectedNote}
            noteIndex={selectedNoteIndex}
            onUpdateNote={handleUpdateSelectedNote}
            {...navigationProps}
          />
        )}
      </div>
    </div>
  )
}