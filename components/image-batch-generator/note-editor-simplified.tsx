"use client"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Card } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { 
  Copy, Download, Upload, Loader2, Image as ImageIcon, 
  Plus, X, Tag, Save, ChevronLeft, ChevronRight,
  ArrowUpDown, AlertTriangle
} from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import type { GeneratedNote } from "@/lib/types/image-batch-generator"

interface NoteEditorSimplifiedProps {
  note: GeneratedNote | undefined
  noteIndex: number
  onUpdateNote: (updates: Partial<GeneratedNote>) => void
  onNavigate?: (direction: 'prev' | 'next') => void
  canNavigate?: { prev: boolean; next: boolean }
}

export function NoteEditorSimplified({ 
  note, 
  noteIndex,
  onUpdateNote,
  onNavigate,
  canNavigate = { prev: false, next: false }
}: NoteEditorSimplifiedProps) {
  // 编辑状态
  const [editTitle, setEditTitle] = useState(note?.title || "")
  const [editContent, setEditContent] = useState(note?.content || "")
  const [contentImages, setContentImages] = useState<string[]>(note?.images || [])
  const [editCoverImage, setEditCoverImage] = useState<string | undefined>(note?.coverImage)
  const [previewImage, setPreviewImage] = useState<string | undefined>(note?.coverImage)
  const [tags, setTags] = useState<string[]>(note?.keywords || [])
  const [newTag, setNewTag] = useState("")
  
  // UI状态
  const [isUploading, setIsUploading] = useState(false)
  const [selectedImageIndex, setSelectedImageIndex] = useState(0)
  const [deleteImageIndex, setDeleteImageIndex] = useState<number | null>(null)
  
  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null)
  const coverFileInputRef = useRef<HTMLInputElement>(null)

  // 同步props的变化
  useEffect(() => {
    if (note) {
      setEditTitle(note.title)
      setEditContent(note.content)
      setContentImages(note.images)
      setEditCoverImage(note.coverImage)
      setPreviewImage(note.coverImage)
      setTags(note.keywords)
      setSelectedImageIndex(0)
    }
  }, [note])

  if (!note) {
    return (
      <div className="flex-1 flex items-center justify-center text-muted-foreground">
        <div className="text-center space-y-4">
          <ImageIcon className="h-16 w-16 mx-auto opacity-20" />
          <p className="text-lg">选择一个笔记开始编辑</p>
        </div>
      </div>
    )
  }

  // 文件上传处理
  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>, isCover = false) => {
    const files = e.target.files
    if (!files || files.length === 0) return

    setIsUploading(true)
    try {
      const newImages: string[] = []
      
      for (const file of Array.from(files)) {
        const reader = new FileReader()
        const result = await new Promise<string>((resolve) => {
          reader.onload = (e) => resolve(e.target?.result as string)
          reader.readAsDataURL(file)
        })
        
        if (isCover) {
          setEditCoverImage(result)
          onUpdateNote({ coverImage: result })
          toast({ title: "封面上传成功" })
          break
        } else {
          newImages.push(result)
        }
      }
      
      if (!isCover && newImages.length > 0) {
        const updatedImages = [...contentImages, ...newImages]
        setContentImages(updatedImages)
        onUpdateNote({ images: updatedImages })
        toast({
          title: "上传成功",
          description: `已添加 ${newImages.length} 张图片`
        })
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "上传失败",
        description: "请稍后重试"
      })
    } finally {
      setIsUploading(false)
    }
  }

  // 删除图片
  const handleRemoveImage = (index: number) => {
    const removedImage = contentImages[index]
    const updatedImages = contentImages.filter((_, i) => i !== index)
    setContentImages(updatedImages)
    onUpdateNote({ images: updatedImages })
    
    if (selectedImageIndex >= updatedImages.length) {
      setSelectedImageIndex(Math.max(0, updatedImages.length - 1))
    }
    
    // 如果删除的是封面图，自动设置新封面
    if (removedImage === editCoverImage) {
      const newCover = updatedImages[0]
      setEditCoverImage(newCover)
      onUpdateNote({ coverImage: newCover })
    }
    
    // 如果删除的是预览图，重置预览
    if (removedImage === previewImage) {
      setPreviewImage(updatedImages[0] || editCoverImage)
    }
    
    setDeleteImageIndex(null)
    toast({ title: "图片已删除" })
  }

  // 设置为封面
  const handleSetAsCover = (image: string) => {
    setEditCoverImage(image)
    onUpdateNote({ coverImage: image })
    toast({ title: "已设为封面" })
  }

  // 添加标签
  const handleAddTag = () => {
    if (!newTag.trim() || tags.includes(newTag.trim())) return
    
    const updatedTags = [...tags, newTag.trim()]
    setTags(updatedTags)
    onUpdateNote({ keywords: updatedTags })
    setNewTag("")
  }

  // 删除标签
  const handleRemoveTag = (tag: string) => {
    const updatedTags = tags.filter(t => t !== tag)
    setTags(updatedTags)
    onUpdateNote({ keywords: updatedTags })
  }

  // 复制内容
  const handleCopy = () => {
    const content = `${editTitle}\n\n${editContent}\n\n${tags.map(t => `#${t}`).join(' ')}`
    navigator.clipboard.writeText(content)
    toast({
      title: "复制成功",
      description: "内容已复制到剪贴板"
    })
  }

  // 下载封面
  const handleDownload = () => {
    if (!editCoverImage) return
    
    const link = document.createElement('a')
    link.href = editCoverImage
    link.download = `note_${noteIndex + 1}_cover.jpg`
    link.click()
  }

  // 自动保存
  const handleAutoSave = () => {
    onUpdateNote({
      title: editTitle,
      content: editContent,
      coverImage: editCoverImage,
      images: contentImages,
      keywords: tags
    })
  }

  return (
    <div className="flex flex-col h-full bg-background">
      {/* 顶部工具栏 */}
      <div className="flex items-center justify-between px-6 py-4 border-b">
        <div className="flex items-center gap-3">
          <h3 className="text-lg font-semibold">笔记 #{noteIndex + 1}</h3>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onNavigate?.('prev')}
              disabled={!canNavigate.prev}
              className="h-8 w-8"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onNavigate?.('next')}
              disabled={!canNavigate.next}
              className="h-8 w-8"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" onClick={handleCopy}>
            <Copy className="h-4 w-4 mr-2" />
            复制
          </Button>
          <Button variant="ghost" size="sm" onClick={handleDownload} disabled={!editCoverImage}>
            <Download className="h-4 w-4 mr-2" />
            下载封面
          </Button>
          <Button size="sm" onClick={handleAutoSave}>
            <Save className="h-4 w-4 mr-2" />
            保存
          </Button>
        </div>
      </div>

      <div className="flex-1 flex overflow-hidden">
        {/* 左侧编辑区 */}
        <div className="flex-1 p-6 overflow-y-auto">
          <div className="max-w-2xl mx-auto space-y-6">
            {/* 标题编辑 */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">标题</Label>
              <Input 
                value={editTitle} 
                onChange={(e) => {
                  setEditTitle(e.target.value)
                  onUpdateNote({ title: e.target.value })
                }} 
                placeholder="输入笔记标题"
                className="text-lg"
              />
            </div>
            
            {/* 内容编辑 */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">内容</Label>
                <span className="text-xs text-muted-foreground">{editContent.length} 字</span>
              </div>
              <Textarea
                value={editContent}
                onChange={(e) => {
                  setEditContent(e.target.value)
                  onUpdateNote({ content: e.target.value })
                }}
                placeholder="输入笔记内容..."
                className="min-h-[300px] resize-none"
              />
            </div>

            {/* 标签管理 */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">标签</Label>
              <div className="flex gap-2">
                <Input
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  placeholder="添加标签"
                  onKeyPress={(e) => e.key === "Enter" && handleAddTag()}
                  className="flex-1"
                />
                <Button onClick={handleAddTag} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {tags.map((tag, index) => (
                  <Badge
                    key={index}
                    variant="secondary"
                    className="group cursor-pointer hover:bg-destructive hover:text-destructive-foreground transition-colors"
                    onClick={() => handleRemoveTag(tag)}
                  >
                    <Tag className="h-3 w-3 mr-1" />
                    {tag}
                    <X className="h-3 w-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity" />
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </div>

        <Separator orientation="vertical" />

        {/* 右侧图片区 */}
        <div className="w-[400px] flex flex-col bg-muted/30">
          {/* 图片预览 */}
          <div className="p-4 border-b">
            <div className="flex items-center justify-between mb-2">
              <Label className="text-sm font-medium">
                {previewImage === editCoverImage ? '封面图' : '图片预览'}
              </Label>
              <input
                type="file"
                ref={coverFileInputRef}
                accept="image/*"
                className="hidden"
                onChange={(e) => handleFileUpload(e, true)}
              />
              {previewImage === editCoverImage && (
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={() => coverFileInputRef.current?.click()}
                  disabled={isUploading}
                >
                  <Upload className="h-4 w-4 mr-2" />
                  更换封面
                </Button>
              )}
            </div>
            <div className="aspect-[4/3] rounded-lg overflow-hidden bg-muted/50">
              {previewImage || editCoverImage ? (
                <img
                  src={previewImage || editCoverImage}
                  alt="预览"
                  className="w-full h-full object-contain"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <ImageIcon className="h-12 w-12 text-muted-foreground/50" />
                </div>
              )}
            </div>
          </div>

          {/* 内容图片管理 */}
          <div className="flex-1 p-4 overflow-hidden flex flex-col">
            <div className="flex items-center justify-between mb-3">
              <Label className="text-sm font-medium">
                内容图片 ({contentImages.length})
              </Label>
              <input
                type="file"
                ref={fileInputRef}
                accept="image/*"
                multiple
                className="hidden"
                onChange={handleFileUpload}
              />
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => fileInputRef.current?.click()}
                disabled={isUploading}
              >
                <Plus className="h-4 w-4 mr-2" />
                添加
              </Button>
            </div>

            {contentImages.length > 0 ? (
              <ScrollArea className="flex-1">
                <div className="grid grid-cols-3 gap-2">
                  {contentImages.map((image, index) => (
                    <div
                      key={index}
                      className={`relative group cursor-pointer bg-muted/50 rounded-md overflow-hidden ${
                        image === editCoverImage ? 'ring-2 ring-primary' : ''
                      } ${image === previewImage ? 'ring-2 ring-blue-500' : ''}`}
                      onClick={() => {
                        setSelectedImageIndex(index)
                        setPreviewImage(image)
                      }}
                    >
                      <img
                        src={image}
                        alt={`图片 ${index + 1}`}
                        className="w-full aspect-square object-contain"
                      />
                      <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-1">
                        <Button
                          variant="secondary"
                          size="icon"
                          className="h-7 w-7"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleSetAsCover(image)
                          }}
                        >
                          <ImageIcon className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="destructive"
                          size="icon"
                          className="h-7 w-7"
                          onClick={(e) => {
                            e.stopPropagation()
                            setDeleteImageIndex(index)
                          }}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                      <div className="absolute top-1 left-1 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded">
                        {index + 1}
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            ) : (
              <div className="flex-1 flex items-center justify-center">
                <div className="text-center text-muted-foreground">
                  <ArrowUpDown className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">暂无内容图片</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 删除确认对话框 */}
      <AlertDialog 
        open={deleteImageIndex !== null} 
        onOpenChange={(open) => !open && setDeleteImageIndex(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除图片</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除这张图片吗？此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="flex items-center gap-2 py-2 text-amber-600">
            <AlertTriangle className="h-5 w-5" />
            <span className="text-sm">
              {deleteImageIndex !== null && contentImages[deleteImageIndex] === editCoverImage
                ? "注意：这是当前的封面图，删除后将自动设置新的封面"
                : "图片删除后将无法恢复"}
            </span>
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => deleteImageIndex !== null && handleRemoveImage(deleteImageIndex)}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              确认删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}