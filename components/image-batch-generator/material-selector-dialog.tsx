'use client';

import { useState, useEffect, useCallback } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Search,
  Folder,
  Tag,
  Image as ImageIcon,
  X,
  Loader2,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';
import type { UploadedImage } from '@/lib/types/image-batch-generator';

interface Material {
  id: string;
  fileName: string;
  displayName: string;
  fileType: string;
  fileSize: number;
  fileUrl: string;
  thumbnailUrl?: string;
  folderId?: string;
  description?: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
}

interface MaterialFolder {
  id: string;
  name: string;
  parentId?: string;
  path: string;
  description?: string;
  createdAt: string;
}

interface MaterialTag {
  id: string;
  name: string;
  color?: string;
}

interface MaterialSelectorDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSelectMaterials: (materials: UploadedImage[]) => void;
  currentImages: UploadedImage[];
}

export function MaterialSelectorDialog({
  open,
  onOpenChange,
  onSelectMaterials,
  currentImages,
}: MaterialSelectorDialogProps) {
  const { toast } = useToast();
  const [materials, setMaterials] = useState<Material[]>([]);
  const [folders, setFolders] = useState<MaterialFolder[]>([]);
  const [tags, setTags] = useState<MaterialTag[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedMaterials, setSelectedMaterials] = useState<Set<string>>(new Set());
  const [searchKeyword, setSearchKeyword] = useState('');
  const [selectedFolder, setSelectedFolder] = useState<string | null>(null);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState<'all' | 'images'>('images');

  // Load materials
  const loadMaterials = useCallback(async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      
      // Only load images for the image batch generator
      if (activeTab === 'images') {
        // We don't have a specific fileType filter in the API,
        // so we'll filter on the client side
      }
      
      if (selectedFolder) params.append('folderId', selectedFolder);
      if (selectedTags.length > 0) params.append('tags', JSON.stringify(selectedTags));
      if (searchKeyword) params.append('keyword', searchKeyword);

      const response = await fetch(`/api/materials?${params}`);
      if (!response.ok) throw new Error('Failed to load materials');
      
      const data = await response.json();
      
      // Filter only image files
      const imageMaterials = data.materials.filter((m: Material) => 
        m.fileType.startsWith('image/')
      );
      
      setMaterials(imageMaterials);
    } catch (error) {
      console.error('Error loading materials:', error);
      toast({
        title: '加载失败',
        description: '无法加载素材列表',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [activeTab, selectedFolder, selectedTags, searchKeyword, toast]);

  // Load folders
  const loadFolders = useCallback(async () => {
    try {
      const response = await fetch('/api/materials/folders');
      if (!response.ok) throw new Error('Failed to load folders');
      
      const data = await response.json();
      setFolders(data.folders);
    } catch (error) {
      console.error('Error loading folders:', error);
    }
  }, []);

  // Load tags
  const loadTags = useCallback(async () => {
    try {
      const response = await fetch('/api/materials/tags');
      if (!response.ok) throw new Error('Failed to load tags');
      
      const data = await response.json();
      setTags(data.tags);
    } catch (error) {
      console.error('Error loading tags:', error);
    }
  }, []);

  // Load data when dialog opens
  useEffect(() => {
    if (open) {
      loadMaterials();
      loadFolders();
      loadTags();
    }
  }, [open, loadMaterials, loadFolders, loadTags]);

  // Toggle material selection
  const toggleMaterialSelection = (materialId: string) => {
    const newSelection = new Set(selectedMaterials);
    if (newSelection.has(materialId)) {
      newSelection.delete(materialId);
    } else {
      newSelection.add(materialId);
    }
    setSelectedMaterials(newSelection);
  };

  // Select all materials
  const selectAllMaterials = () => {
    const allIds = materials.map(m => m.id);
    setSelectedMaterials(new Set(allIds));
  };

  // Clear selection
  const clearSelection = () => {
    setSelectedMaterials(new Set());
  };

  // Handle confirm selection
  const handleConfirmSelection = async () => {
    if (selectedMaterials.size === 0) {
      toast({
        title: '请选择素材',
        description: '请至少选择一张图片',
        variant: 'destructive',
      });
      return;
    }

    const selectedMaterialsList = materials.filter(m => selectedMaterials.has(m.id));
    const newImages: UploadedImage[] = [];
    
    // Get the current max order
    const maxOrder = Math.max(0, ...currentImages.map(img => img.order));
    
    for (let i = 0; i < selectedMaterialsList.length; i++) {
      const material = selectedMaterialsList[i];
      
      // Create a mock File object for materials
      // The actual file will be fetched when needed during processing
      const mockFile = new File([], material.fileName, { type: material.fileType });
      // Add the URL as a property for later use
      (mockFile as any).materialUrl = material.fileUrl;
      
      newImages.push({
        id: `material-${material.id}`,
        file: mockFile,
        preview: material.thumbnailUrl || material.fileUrl,
        order: maxOrder + i + 1,
      });
    }
    
    if (newImages.length > 0) {
      onSelectMaterials(newImages);
      toast({
        title: '添加成功',
        description: `已添加 ${newImages.length} 张图片`,
      });
    }
    
    // Reset and close
    setSelectedMaterials(new Set());
    onOpenChange(false);
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '未知大小';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle>从素材库选择图片</DialogTitle>
          <DialogDescription>
            选择素材库中的图片添加到批量生成器
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Search and filters */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="搜索素材..."
                value={searchKeyword}
                onChange={(e) => setSearchKeyword(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={selectedFolder || 'all'} onValueChange={(value) => setSelectedFolder(value === 'all' ? null : value)}>
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="选择文件夹" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有文件夹</SelectItem>
                {folders.map((folder) => (
                  <SelectItem key={folder.id} value={folder.id}>
                    <Folder className="w-4 h-4 inline mr-2" />
                    {folder.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Tags filter */}
          {tags.length > 0 && (
            <div className="flex items-center gap-2 flex-wrap">
              <span className="text-sm text-muted-foreground">标签筛选：</span>
              {tags.map((tag) => (
                <Badge
                  key={tag.id}
                  variant={selectedTags.includes(tag.id) ? 'default' : 'outline'}
                  className="cursor-pointer"
                  onClick={() => {
                    if (selectedTags.includes(tag.id)) {
                      setSelectedTags(selectedTags.filter((t) => t !== tag.id));
                    } else {
                      setSelectedTags([...selectedTags, tag.id]);
                    }
                  }}
                  style={{
                    backgroundColor: selectedTags.includes(tag.id) ? tag.color : undefined,
                    borderColor: tag.color,
                  }}
                >
                  {tag.name}
                </Badge>
              ))}
            </div>
          )}

          {/* Selection info */}
          {selectedMaterials.size > 0 && (
            <div className="flex items-center justify-between p-3 bg-primary/10 rounded-lg">
              <span className="text-sm font-medium">
                已选择 {selectedMaterials.size} 张图片
              </span>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={selectAllMaterials}
                >
                  全选
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearSelection}
                >
                  清除选择
                </Button>
              </div>
            </div>
          )}

          {/* Materials grid */}
          <ScrollArea className="h-[400px]">
            {loading ? (
              <div className="flex items-center justify-center h-64">
                <Loader2 className="w-8 h-8 animate-spin" />
              </div>
            ) : materials.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
                <ImageIcon className="w-12 h-12 mb-4" />
                <p>暂无图片素材</p>
              </div>
            ) : (
              <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 p-4">
                {materials.map((material) => (
                  <div
                    key={material.id}
                    className={cn(
                      "relative border rounded-lg overflow-hidden cursor-pointer hover:shadow-lg transition-shadow",
                      selectedMaterials.has(material.id) && "ring-2 ring-primary"
                    )}
                    onClick={() => toggleMaterialSelection(material.id)}
                  >
                    {/* Selection checkbox */}
                    <div className="absolute top-2 left-2 z-10">
                      <Checkbox
                        checked={selectedMaterials.has(material.id)}
                        onCheckedChange={() => toggleMaterialSelection(material.id)}
                        onClick={(e) => e.stopPropagation()}
                      />
                    </div>
                    
                    {/* Thumbnail */}
                    <div className="aspect-square bg-muted relative">
                      {material.thumbnailUrl || material.fileUrl ? (
                        <img
                          src={material.thumbnailUrl || material.fileUrl}
                          alt={material.displayName}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <ImageIcon className="w-8 h-8 text-muted-foreground" />
                        </div>
                      )}
                    </div>
                    
                    {/* Info */}
                    <div className="p-2">
                      <p className="text-sm font-medium truncate" title={material.displayName}>
                        {material.displayName}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {formatFileSize(material.fileSize)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </ScrollArea>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button 
            onClick={handleConfirmSelection}
            disabled={selectedMaterials.size === 0}
          >
            添加选中的图片 ({selectedMaterials.size})
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}