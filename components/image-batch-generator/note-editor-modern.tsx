"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Card } from "@/components/ui/card"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { 
  Copy, Download, Upload, Loader2, ImageIcon, 
  Plus, X, Hash, ArrowLeft, ArrowRight, 
  FileImage, Edit2, AlertTriangle
} from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import type { GeneratedNote } from "@/lib/types/image-batch-generator"

interface NoteEditorModernProps {
  note: GeneratedNote | undefined
  noteIndex: number
  onUpdateNote: (updates: Partial<GeneratedNote>) => void
  onNavigate?: (direction: 'prev' | 'next') => void
  canNavigate?: { prev: boolean; next: boolean }
}

export function NoteEditorModern({ 
  note, 
  noteIndex,
  onUpdateNote,
  onNavigate,
  canNavigate = { prev: false, next: false }
}: NoteEditorModernProps) {
  const [editTitle, setEditTitle] = useState(note?.title || "")
  const [editContent, setEditContent] = useState(note?.content || "")
  const [contentImages, setContentImages] = useState<string[]>(note?.images || [])
  const [editCoverImage, setEditCoverImage] = useState<string | undefined>(note?.coverImage)
  const [previewImage, setPreviewImage] = useState<string | undefined>(note?.coverImage)
  const [tags, setTags] = useState<string[]>(note?.keywords || [])
  const [newTag, setNewTag] = useState("")
  const [isEditingTag, setIsEditingTag] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [deleteImageIndex, setDeleteImageIndex] = useState<number | null>(null)
  
  const fileInputRef = useRef<HTMLInputElement>(null)
  const tagInputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (note) {
      setEditTitle(note.title)
      setEditContent(note.content)
      setContentImages(note.images)
      setEditCoverImage(note.coverImage)
      setPreviewImage(note.coverImage)
      setTags(note.keywords)
    }
  }, [note])

  useEffect(() => {
    if (isEditingTag && tagInputRef.current) {
      tagInputRef.current.focus()
    }
  }, [isEditingTag])

  if (!note) {
    return (
      <div className="flex-1 flex items-center justify-center text-muted-foreground">
        <div className="text-center space-y-4">
          <Edit2 className="h-16 w-16 mx-auto opacity-20" />
          <p className="text-lg">选择一个笔记开始编辑</p>
        </div>
      </div>
    )
  }

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (!files || files.length === 0) return

    setIsUploading(true)
    try {
      const newImages: string[] = []
      
      for (const file of Array.from(files)) {
        const reader = new FileReader()
        const result = await new Promise<string>((resolve) => {
          reader.onload = (e) => resolve(e.target?.result as string)
          reader.readAsDataURL(file)
        })
        newImages.push(result)
      }
      
      const updatedImages = [...contentImages, ...newImages]
      setContentImages(updatedImages)
      onUpdateNote({ images: updatedImages })
      
      // 如果还没有封面，将第一张设为封面
      if (!editCoverImage && newImages.length > 0) {
        setEditCoverImage(newImages[0])
        setPreviewImage(newImages[0])
        onUpdateNote({ coverImage: newImages[0] })
      }
      
      toast({
        title: "上传成功",
        description: `已添加 ${newImages.length} 张图片`
      })
    } catch (error) {
      toast({
        variant: "destructive",
        title: "上传失败"
      })
    } finally {
      setIsUploading(false)
    }
  }

  const handleRemoveImage = (index: number) => {
    const removedImage = contentImages[index]
    const updatedImages = contentImages.filter((_, i) => i !== index)
    setContentImages(updatedImages)
    onUpdateNote({ images: updatedImages })
    
    // 如果删除的是封面图，自动设置新封面
    if (removedImage === editCoverImage) {
      const newCover = updatedImages[0]
      setEditCoverImage(newCover)
      onUpdateNote({ coverImage: newCover })
    }
    
    // 如果删除的是预览图，重置预览
    if (removedImage === previewImage) {
      setPreviewImage(updatedImages[0] || editCoverImage)
    }
    
    setDeleteImageIndex(null)
    toast({ title: "图片已删除" })
  }

  const handleSetAsCover = (image: string) => {
    setEditCoverImage(image)
    onUpdateNote({ coverImage: image })
    toast({ title: "已设为封面" })
  }

  const handleAddTag = () => {
    if (!newTag.trim() || tags.includes(newTag.trim())) {
      setIsEditingTag(false)
      setNewTag("")
      return
    }
    
    const updatedTags = [...tags, newTag.trim()]
    setTags(updatedTags)
    onUpdateNote({ keywords: updatedTags })
    setNewTag("")
    setIsEditingTag(false)
  }

  const handleRemoveTag = (tag: string) => {
    const updatedTags = tags.filter(t => t !== tag)
    setTags(updatedTags)
    onUpdateNote({ keywords: updatedTags })
  }

  const handleCopy = () => {
    const content = `${editTitle}\n\n${editContent}\n\n${tags.map(t => `#${t}`).join(' ')}`
    navigator.clipboard.writeText(content)
    toast({ title: "已复制到剪贴板" })
  }

  const handleDownload = () => {
    if (!editCoverImage) return
    const link = document.createElement('a')
    link.href = editCoverImage
    link.download = `note_${noteIndex + 1}_cover.jpg`
    link.click()
  }

  return (
    <div className="flex flex-col h-full bg-background">
      {/* 精简的顶部导航 */}
      <div className="flex items-center justify-between px-6 py-3 border-b bg-muted/30">
        <div className="flex items-center gap-4">
          <span className="text-sm text-muted-foreground">笔记</span>
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={() => onNavigate?.('prev')}
              disabled={!canNavigate.prev}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <span className="px-3 font-medium">#{noteIndex + 1}</span>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={() => onNavigate?.('next')}
              disabled={!canNavigate.next}
            >
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" onClick={handleCopy}>
            <Copy className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" onClick={handleDownload} disabled={!editCoverImage}>
            <Download className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto">
        <div className="max-w-3xl mx-auto p-6 space-y-6">
          {/* 封面和图片管理合并区域 */}
          <Card className="p-6">
            <div className="space-y-4">
              {/* 大图预览 */}
              {(previewImage || editCoverImage) && (
                <div className="relative group">
                  <div className="aspect-[16/9] rounded-lg overflow-hidden bg-muted/50">
                    <img
                      src={previewImage || editCoverImage}
                      alt="预览"
                      className="w-full h-full object-contain"
                    />
                  </div>
                  {previewImage === editCoverImage && (
                    <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                      <Badge variant="secondary" className="text-xs">
                        <FileImage className="h-3 w-3 mr-1" />
                        封面图
                      </Badge>
                    </div>
                  )}
                </div>
              )}

              {/* 图片网格 */}
              <div className="grid grid-cols-4 sm:grid-cols-6 gap-2">
                {contentImages.map((image, index) => (
                  <div
                    key={index}
                    className={`relative group cursor-pointer aspect-square rounded-md overflow-hidden bg-muted/50 ${
                      image === editCoverImage ? 'ring-2 ring-primary' : ''
                    } ${image === previewImage ? 'ring-2 ring-blue-500' : ''}`}
                    onClick={() => setPreviewImage(image)}
                  >
                    <img
                      src={image}
                      alt={`图片 ${index + 1}`}
                      className="w-full h-full object-contain"
                    />
                    <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                      <div className="flex gap-1">
                        {image !== editCoverImage && (
                          <Button
                            variant="secondary"
                            size="icon"
                            className="h-6 w-6"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleSetAsCover(image)
                            }}
                          >
                            <FileImage className="h-3 w-3" />
                          </Button>
                        )}
                        <Button
                          variant="secondary"
                          size="icon"
                          className="h-6 w-6"
                          onClick={(e) => {
                            e.stopPropagation()
                            setDeleteImageIndex(index)
                          }}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
                
                {/* 添加图片按钮 */}
                <input
                  type="file"
                  ref={fileInputRef}
                  accept="image/*"
                  multiple
                  className="hidden"
                  onChange={handleFileUpload}
                />
                <button
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isUploading}
                  className="aspect-square rounded-md border-2 border-dashed border-muted-foreground/20 hover:border-muted-foreground/40 transition-colors flex items-center justify-center group"
                >
                  {isUploading ? (
                    <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                  ) : (
                    <Plus className="h-6 w-6 text-muted-foreground group-hover:scale-110 transition-transform" />
                  )}
                </button>
              </div>
            </div>
          </Card>

          {/* 标题输入 */}
          <div className="space-y-2">
            <Input 
              value={editTitle} 
              onChange={(e) => {
                setEditTitle(e.target.value)
                onUpdateNote({ title: e.target.value })
              }} 
              placeholder="输入标题..."
              className="text-xl font-medium border-0 px-0 focus-visible:ring-0 focus-visible:ring-offset-0"
            />
          </div>

          {/* 内容输入 */}
          <div className="space-y-2">
            <Textarea
              value={editContent}
              onChange={(e) => {
                setEditContent(e.target.value)
                onUpdateNote({ content: e.target.value })
              }}
              placeholder="分享你的想法..."
              className="min-h-[200px] resize-none border-0 px-0 text-base focus-visible:ring-0 focus-visible:ring-offset-0"
            />
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>{editContent.length} 字</span>
            </div>
          </div>

          {/* 标签区域 */}
          <div className="flex flex-wrap items-center gap-2">
            {tags.map((tag, index) => (
              <Badge
                key={index}
                variant="secondary"
                className="pl-1.5 pr-1 py-1 cursor-pointer hover:bg-destructive/10 hover:text-destructive group transition-colors"
                onClick={() => handleRemoveTag(tag)}
              >
                <Hash className="h-3 w-3 mr-0.5" />
                {tag}
                <X className="h-3 w-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity" />
              </Badge>
            ))}
            
            {isEditingTag ? (
              <div className="flex items-center">
                <span className="text-muted-foreground mr-1">#</span>
                <input
                  ref={tagInputRef}
                  type="text"
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  onBlur={handleAddTag}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault()
                      handleAddTag()
                    }
                  }}
                  className="bg-transparent outline-none text-sm w-24"
                  placeholder="标签"
                />
              </div>
            ) : (
              <Button
                variant="ghost"
                size="sm"
                className="h-7 px-2 text-xs"
                onClick={() => setIsEditingTag(true)}
              >
                <Plus className="h-3 w-3 mr-1" />
                添加标签
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* 删除确认对话框 */}
      <AlertDialog 
        open={deleteImageIndex !== null} 
        onOpenChange={(open) => !open && setDeleteImageIndex(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除图片</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除这张图片吗？此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="flex items-center gap-2 py-2 text-amber-600">
            <AlertTriangle className="h-5 w-5" />
            <span className="text-sm">
              {deleteImageIndex !== null && contentImages[deleteImageIndex] === editCoverImage
                ? "注意：这是当前的封面图，删除后将自动设置新的封面"
                : "图片删除后将无法恢复"}
            </span>
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => deleteImageIndex !== null && handleRemoveImage(deleteImageIndex)}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              确认删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}